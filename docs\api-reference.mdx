---
title: Overview
icon: "info"
iconType: "solid"
---

<Snippet file="async-memory-add.mdx" />

Mem0 provides a powerful set of APIs that allow you to integrate advanced memory management capabilities into your applications. Our APIs are designed to be intuitive, efficient, and scalable, enabling you to create, retrieve, update, and delete memories across various entities such as users, agents, apps, and runs.

## Key Features

- **Memory Management**: Add, retrieve, update, and delete memories with ease.
- **Entity-based Operations**: Perform operations on memories associated with specific users, agents, apps, or runs.
- **Advanced Search**: Utilize our search API to find relevant memories based on various criteria.
- **History Tracking**: Access the history of memory interactions for comprehensive analysis.
- **User Management**: Manage user entities and their associated memories.

## API Structure

Our API is organized into several main categories:

1. **Memory APIs**: Core operations for managing individual memories and collections.
2. **Entities APIs**: Manage different entity types (users, agents, etc.) and their associated memories.
3. **Search API**: Advanced search functionality to retrieve relevant memories.
4. **History API**: Track and retrieve the history of memory interactions.

## Authentication

All API requests require authentication using HTTP Basic Auth. Ensure you include your API key in the Authorization header of each request.

## Organizations and projects (optional)

Organizations and projects provide the following capabilities:

- **Multi-org/project Support**: Specify organization and project when initializing the Mem0 client to attribute API usage appropriately
- **Member Management**: Control access to data through organization and project membership
- **Access Control**: Only members can access memories and data within their organization/project scope
- **Team Isolation**: Maintain data separation between different teams and projects for secure collaboration

Example with the mem0 Python package:

<Tabs>
  <Tab title="Python">

```python
from mem0 import MemoryClient
client = MemoryClient(org_id='YOUR_ORG_ID', project_id='YOUR_PROJECT_ID')
```

  </Tab>

  <Tab title="Node.js">

```javascript
import { MemoryClient } from "mem0ai";
const client = new MemoryClient({organizationId: "YOUR_ORG_ID", projectId: "YOUR_PROJECT_ID"});
```

  </Tab>
</Tabs>

### Project Management Methods

The Mem0 client provides comprehensive project management capabilities through the `client.project` interface:

#### Get Project Details

Retrieve information about the current project:

```python
# Get all project details
project_info = client.project.get()

# Get specific fields only
project_info = client.project.get(fields=["name", "description", "custom_categories"])
```

#### Create a New Project

Create a new project within your organization:

```python
# Create a project with name and description
new_project = client.project.create(
    name="My New Project",
    description="A project for managing customer support memories"
)
```

#### Update Project Settings

Modify project configuration including custom instructions, categories, and graph settings:

```python
# Update project with custom categories
client.project.update(
    custom_categories=[
        {"customer_preferences": "Customer likes, dislikes, and preferences"},
        {"support_history": "Previous support interactions and resolutions"}
    ]
)

# Update project with custom instructions
client.project.update(
    custom_instructions="..."
)

# Enable graph memory for the project
client.project.update(enable_graph=True)

# Update multiple settings at once
client.project.update(
    custom_instructions="...",
    custom_categories=[
        {"personal_info": "User personal information and preferences"},
        {"work_context": "Professional context and work-related information"}
    ],
    enable_graph=True
)
```

#### Delete Project

<Note>
This action will remove all memories, messages, and other related data in the project. This operation is irreversible.
</Note>

Remove a project and all its associated data:

```python
# Delete the current project (irreversible)
result = client.project.delete()
```

#### Member Management

Manage project members and their access levels:

```python
# Get all project members
members = client.project.get_members()

# Add a new member as a reader
client.project.add_member(
    email="<EMAIL>",
    role="READER"  # or "OWNER"
)

# Update a member's role
client.project.update_member(
    email="<EMAIL>",
    role="OWNER"
)

# Remove a member from the project
client.project.remove_member(email="<EMAIL>")
```

#### Member Roles

- **READER**: Can view and search memories, but cannot modify project settings or manage members
- **OWNER**: Full access including project modification, member management, and all reader permissions

#### Async Support

All project methods are also available in async mode:

```python
from mem0 import AsyncMemoryClient

async def manage_project():
    client = AsyncMemoryClient(org_id='YOUR_ORG_ID', project_id='YOUR_PROJECT_ID')

    # All methods support async/await
    project_info = await client.project.get()
    await client.project.update(enable_graph=True)
    members = await client.project.get_members()

# To call the async function properly
import asyncio
asyncio.run(manage_project())
```

## Getting Started

To begin using the Mem0 API, you'll need to:

1. Sign up for a [Mem0 account](https://app.mem0.ai) and obtain your API key.
2. Familiarize yourself with the API endpoints and their functionalities.
3. Make your first API call to add or retrieve a memory.

Explore the detailed documentation for each API endpoint to learn more about request/response formats, parameters, and example usage.
