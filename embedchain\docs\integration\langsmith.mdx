---
title: '🛠️ LangSmith'
description: 'Integrate with <PERSON><PERSON> to debug and monitor your LLM app'
---

Embedchain now supports integration with [LangSmith](https://www.langchain.com/langsmith).

To use LangSmith, you need to do the following steps.

1. Have an account on Lang<PERSON><PERSON> and keep the environment variables in handy
2. Set the environment variables in your app so that embedchain has context about it.
3. Just use embedchain and everything will be logged to LangSmith, so that you can better test and monitor your application.

Let's cover each step in detail.


* First make sure that you have created a LangSmith account and have all the necessary variables handy. LangSmith has a [good documentation](https://docs.smith.langchain.com/) on how to get started with their service.

* Once you have setup the account, we will need the following environment variables

```bash
# Setting environment variable for LangChain Tracing V2 integration.
export LANGCHAIN_TRACING_V2=true

# Setting the API endpoint for LangChain.
export LANGCHAIN_ENDPOINT=https://api.smith.langchain.com

# Replace '<your-api-key>' with your LangChain API key.
export LANGCHAIN_API_KEY=<your-api-key>

# Replace '<your-project>' with your LangChain project name, or it defaults to "default".
export LANGCHAIN_PROJECT=<your-project>  # if not specified, defaults to "default"
```

If you are using Python, you can use the following code to set environment variables

```python
import os

# Setting environment variable for LangChain Tracing V2 integration.
os.environ['LANGCHAIN_TRACING_V2'] = 'true'

# Setting the API endpoint for LangChain.
os.environ['LANGCHAIN_ENDPOINT'] = 'https://api.smith.langchain.com'

# Replace '<your-api-key>' with your LangChain API key.
os.environ['LANGCHAIN_API_KEY'] = '<your-api-key>'

# Replace '<your-project>' with your LangChain project name.
os.environ['LANGCHAIN_PROJECT'] = '<your-project>'
```

* Now create an app using Embedchain and everything will be automatically visible in the LangSmith


```python
from embedchain import App

# Initialize EmbedChain application.
app = App()

# Add data to your app
app.add("https://en.wikipedia.org/wiki/Elon_Musk")

# Query your app
app.query("How many companies did Elon found?")
```

* Now the entire log for this will be visible in langsmith.

<img src="/images/langsmith.png"/>
