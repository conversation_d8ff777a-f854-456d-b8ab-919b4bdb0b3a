---
title: Azure OpenAI
---

To use Azure OpenAI embedding models, set the `EMBEDDING_AZURE_OPENAI_API_KEY`, `EMBEDDING_AZURE_DEPLOYMENT`, `EMBEDDING_AZURE_ENDPOINT` and `EMBEDDING_AZURE_API_VERSION` environment variables. You can obtain the Azure OpenAI API key from the Azure.

### Usage

<CodeGroup>
```python Python
import os
from mem0 import Memory

os.environ["EMBEDDING_AZURE_OPENAI_API_KEY"] = "your-api-key"
os.environ["EMBEDDING_AZURE_DEPLOYMENT"] = "your-deployment-name"
os.environ["EMBEDDING_AZURE_ENDPOINT"] = "your-api-base-url"
os.environ["EMBEDDING_AZURE_API_VERSION"] = "version-to-use"

os.environ["OPENAI_API_KEY"] = "your_api_key" # For LLM


config = {
    "embedder": {
        "provider": "azure_openai",
        "config": {
            "model": "text-embedding-3-large"
            "azure_kwargs": {
                  "api_version": "",
                  "azure_deployment": "",
                  "azure_endpoint": "",
                  "api_key": "",
                  "default_headers": {
                    "CustomHeader": "your-custom-header",
                  }
              }
        }
    }
}

m = Memory.from_config(config)
messages = [
    {"role": "user", "content": "I'm planning to watch a movie tonight. Any recommendations?"},
    {"role": "assistant", "content": "How about a thriller movies? They can be quite engaging."},
    {"role": "user", "content": "I’m not a big fan of thriller movies but I love sci-fi movies."},
    {"role": "assistant", "content": "Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future."}
]
m.add(messages, user_id="john")
```

```typescript TypeScript
import { Memory } from 'mem0ai/oss';

const config = {
    embedder: {
        provider: "azure_openai",
        config: {
            model: "text-embedding-3-large",
            modelProperties: {
                endpoint: "your-api-base-url",
                deployment: "your-deployment-name",
                apiVersion: "version-to-use",
            }
        }
    }
}

const memory = new Memory(config);

const messages = [
    {"role": "user", "content": "I'm planning to watch a movie tonight. Any recommendations?"},
    {"role": "assistant", "content": "How about a thriller movies? They can be quite engaging."},
    {"role": "user", "content": "I’m not a big fan of thriller movies but I love sci-fi movies."},
    {"role": "assistant", "content": "Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future."}
]

await memory.add(messages, { userId: "john" });
```
</CodeGroup>

### Config

Here are the parameters available for configuring Azure OpenAI embedder:

| Parameter | Description | Default Value |
| --- | --- | --- |
| `model` | The name of the embedding model to use | `text-embedding-3-small` |
| `embedding_dims` | Dimensions of the embedding model | `1536` |
| `azure_kwargs` | The Azure OpenAI configs | `config_keys` |
