{"openapi": "3.0.1", "info": {"title": "Mem0 API Docs", "description": "mem0.ai API Docs", "contact": {"email": "<EMAIL>"}, "license": {"name": "Apache 2.0"}, "version": "v1"}, "servers": [{"url": "https://api.mem0.ai/"}], "security": [{"ApiKeyAuth": []}], "paths": {"/v1/agents/": {"post": {"tags": ["agents"], "description": "Create a new Agent.", "operationId": "agents_create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgent"}}}, "required": true}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgent"}}}}}, "x-codegen-request-body-name": "data"}}, "/v1/apps/": {"post": {"tags": ["apps"], "description": "Create a new App.", "operationId": "apps_create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateApp"}}}, "required": true}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateApp"}}}}}, "x-codegen-request-body-name": "data"}}, "/v1/entities/": {"get": {"tags": ["entities"], "operationId": "entities_list", "parameters": [{"name": "org_id", "in": "query", "schema": {"type": "string"}, "description": "Filter entities by organization ID."}, {"name": "project_id", "in": "query", "schema": {"type": "string"}, "description": "Filter entities by project ID."}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the entity"}, "name": {"type": "string", "description": "Name of the entity"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the entity was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the entity was last updated"}, "total_memories": {"type": "integer", "description": "Total number of memories associated with the entity"}, "owner": {"type": "string", "description": "Owner of the entity"}, "organization": {"type": "string", "description": "Organization the entity belongs to"}, "metadata": {"type": "object", "description": "Additional metadata associated with the entity"}, "type": {"type": "string", "enum": ["user", "agent", "app", "run"]}}, "required": ["id", "name", "created_at", "updated_at", "total_memories", "owner", "organization", "type"]}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\nusers = client.users()\nprint(users)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Retrieve all users\nclient.users()\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/v1/entities/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/entities/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/entities/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/v1/entities/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/v1/entities/filters/": {"get": {"tags": ["entities"], "operationId": "entities_filters_list", "responses": {"200": {"description": "", "content": {}}}}}, "/v1/entities/{entity_type}/{entity_id}/": {"get": {"tags": ["entities"], "operationId": "entities_read", "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"type": "string", "enum": ["user", "agent", "app", "run"]}, "description": "The type of the entity (user, agent, app, or run)"}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The unique identifier of the entity"}], "responses": {"200": {"description": "", "content": {}}}}, "delete": {"tags": ["entities"], "operationId": "entities_delete", "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"type": "string", "enum": ["user", "agent", "app", "run"]}, "description": "The type of the entity (user, agent, app, or run)"}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The unique identifier of the entity"}], "responses": {"204": {"description": "Entity deleted successfully!", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Entity deleted successfully!"}}}}}}, "400": {"description": "Invalid entity type", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Invalid entity type"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/v1/entities/{entity_type}/{entity_id}/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"DELETE\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'DELETE', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/v1/entities/{entity_type}/{entity_id}/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request DELETE \\\n  --url https://api.mem0.ai/v1/entities/{entity_type}/{entity_id}/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/entities/{entity_type}/{entity_id}/\"\n\n\treq, _ := http.NewRequest(\"DELETE\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/entities/{entity_type}/{entity_id}/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/v1/entities/{entity_type}/{entity_id}/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/v1/events/": {"get": {"tags": ["events"], "summary": "Retrieve all events for the currently logged-in user.", "description": "This endpoint returns a paginated list of events associated with the authenticated user.\nYou can filter the events by event type, start date, and end date.\n\nQuery Parameters:\n- event_type: Filter by event type (ADD or SEARCH)\n- start_date: Filter events after this date (format: YYYY-MM-DD)\n- end_date: Filter events before this date (format: YYYY-MM-DD)\n- page: Page number for pagination\n- page_size: Number of items per page (default: 50, max: 100)", "operationId": "events_list", "responses": {"200": {"description": "", "content": {}}}}}, "/v1/exports/": {"post": {"tags": ["exports"], "summary": "Create an export job with schema", "description": "Create a structured export of memories based on a provided schema.", "operationId": "exports_create", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["schema"], "properties": {"schema": {"type": "object", "description": "Schema definition for the export"}, "filters": {"type": "object", "properties": {"user_id": {"type": "string"}, "agent_id": {"type": "string"}, "app_id": {"type": "string"}, "run_id": {"type": "string"}}, "description": "Filters to apply while exporting memories. Available fields are: user_id, agent_id, app_id, run_id."}, "org_id": {"type": "string", "description": "Filter exports by organization ID"}, "project_id": {"type": "string", "description": "Filter exports by project ID"}}}}}, "required": true}, "responses": {"201": {"description": "Export created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Memory export request received. The export will be ready in a few seconds."}, "id": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}}, "required": ["message", "id"]}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Schema is required and must be a valid object"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\n\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\njson_schema = {pydantic_json_schema}\nfilters = {\n    \"AND\": [\n        {\"user_id\": \"alex\"}\n    ]\n}\n\nresponse = client.create_memory_export(\n    schema=json_schema,\n    filters=filters\n)\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst jsonSchema = {pydantic_json_schema};\nconst filters = {\n  AND: [\n    {user_id: 'alex'}\n  ]\n};\n\nclient.createMemoryExport({\n  schema: jsonSchema,\n  filters: filters\n})\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url 'https://api.mem0.ai/v1/exports/' \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n    \"schema\": {pydantic_json_schema},\n    \"filters\": {\n      \"AND\": [\n        {\"user_id\": \"alex\"}\n      ]\n    }\n  }'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"bytes\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\turl := \"https://api.mem0.ai/v1/exports/\"\n\n\tfilters := map[string]interface{}{\n\t\t\"AND\": []map[string]interface{}{\n\t\t\t{\"user_id\": \"alex\"},\n\t\t},\n\t}\n\n\tdata := map[string]interface{}{\n\t\t\"schema\": map[string]interface{}{}, // Your schema here\n\t\t\"filters\": filters,\n\t}\n\n\tjsonData, _ := json.Marshal(data)\n\n\treq, _ := http.NewRequest(\"POST\", url, bytes.NewBuffer(jsonData))\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(string(body))\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\n$filters = [\n  'AND' => [\n    ['user_id' => 'alex']\n  ]\n];\n\n$data = array(\n  \"schema\" => array(), // Your schema here\n  \"filters\" => $filters\n);\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/exports/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => json_encode($data),\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "import com.mashape.unirest.http.HttpResponse;\nimport com.mashape.unirest.http.JsonNode;\nimport com.mashape.unirest.http.Unirest;\nimport org.json.JSONObject;\nimport org.json.JSONArray;\n\nJSONObject filters = new JSONObject()\n    .put(\"AND\", new JSONArray()\n        .put(new JSONObject().put(\"user_id\", \"alex\")));\n\nJSONObject data = new JSONObject()\n    .put(\"schema\", new JSONObject()) // Your schema here\n    .put(\"filters\", filters);\n\nHttpResponse<JsonNode> response = Unirest.post(\"https://api.mem0.ai/v1/exports/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(data.toString())\n  .asJson();"}]}}, "/v1/exports/get": {"post": {"tags": ["exports"], "summary": "Export data based on filters", "description": "Get the latest memory export.", "operationId": "exports_list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"memory_export_id": {"type": "string", "description": "The unique identifier of the memory export"}, "filters": {"type": "object", "properties": {"user_id": {"type": "string"}, "agent_id": {"type": "string"}, "app_id": {"type": "string"}, "run_id": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "description": "Filters to apply while exporting memories. Available fields are: user_id, agent_id, app_id, run_id, created_at, updated_at."}, "org_id": {"type": "string", "description": "Filter exports by organization ID"}, "project_id": {"type": "string", "description": "Filter exports by project ID"}}}}}}, "responses": {"200": {"description": "Successful export", "content": {"application/json": {"schema": {"type": "object", "description": "Export data response in a object format"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "One of the filters: app_id, user_id, agent_id, run_id is required!"}}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "No memory export request found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\n\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"project_id\")\n\nmemory_export_id = \"<memory_export_id>\"\n\nresponse = client.get_memory_export(memory_export_id=memory_export_id)\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst memory_export_id = \"<memory_export_id>\";\n\n// Get memory export\nclient.getMemoryExport({ memory_export_id })\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url 'https://api.mem0.ai/v1/exports/get/' \\\n  --header 'Authorization: Token <api-key>' \\\n  --data '{\n    \"memory_export_id\": \"<memory_export_id>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\tmemory_export_id := \"<memory_export_id>\"\n\n\treq, _ := http.NewRequest(\"POST\", \"https://api.mem0.ai/v1/exports/get/\", nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(string(body))\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\n$data = json_encode(['memory_export_id' => '<memory_export_id>']);\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/exports/get/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => $data,\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "String data = \"{\\\"memory_export_id\\\":\\\"<memory_export_id>\\\"}\";\n\nHttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/v1/exports/get/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(data)\n  .asString();"}]}}, "/v1/memories/": {"get": {"tags": ["memories"], "description": "Get all memories", "operationId": "memories_list", "parameters": [{"name": "user_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by user ID"}, {"name": "agent_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by agent ID"}, {"name": "app_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by app ID"}, {"name": "run_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by run ID"}, {"name": "metadata", "in": "query", "schema": {"type": "object"}, "description": "Filter memories by metadata (JSON string)", "style": "deepObject", "explode": true}, {"name": "categories", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Filter memories by categories"}, {"name": "org_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by organization ID."}, {"name": "project_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by project ID."}, {"name": "fields", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Filter memories by fields"}, {"name": "keywords", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by keywords"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "description": "Page number for pagination. Default: 1"}, {"name": "page_size", "in": "query", "schema": {"type": "integer"}, "description": "Number of items per page. Default: 100"}, {"name": "start_date", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by start date"}, {"name": "end_date", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by end date"}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "memory": {"type": "string"}, "input": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}}}}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "owner": {"type": "string"}, "immutable": {"type": "boolean", "description": "Whether the memory is immutable.", "title": "Immutable", "default": false}, "expiration_date": {"type": "string", "format": "date-time", "description": "The date and time when the memory will expire. Format: YYYY-MM-DD", "title": "Expiration date", "nullable": true, "default": null}, "organization": {"type": "string"}, "metadata": {"type": "object"}}, "required": ["id", "memory", "created_at", "updated_at", "total_memories", "owner", "organization", "type"]}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "One of the filters: app_id, user_id, agent_id, run_id is required!"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\n# Retrieve memories for a specific user\nuser_memories = client.get_all(user_id=\"<user_id>\")\n\nprint(user_memories)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Retrieve memories for a specific user\nclient.getAll({ user_id: \"<user_id>\" })\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --location --request GET 'https://api.mem0.ai/v1/memories/' \\\n--header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/v1/memories/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}, "post": {"tags": ["memories"], "description": "Add memories", "operationId": "memories_create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemoryInput"}}}, "required": true}, "responses": {"200": {"description": "Successful memory creation", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "data": {"type": "object", "properties": {"memory": {"type": "string"}}, "required": ["memory"]}, "event": {"type": "string", "enum": ["ADD", "UPDATE", "DELETE"]}}, "required": ["id", "data", "event"]}}}}}, "400": {"description": "Bad Request. Invalid input data. Please refer to the memory creation documentation at https://docs.mem0.ai/platform/quickstart#4-1-create-memories for correct formatting and required fields.", "content": {"application/json": {"schema": {"type": "object", "required": ["error", "details"], "example": {"error": "400 Bad Request", "details": {"message": "Invalid input data. Please refer to the memory creation documentation at https://docs.mem0.ai/platform/quickstart#4-1-create-memories for correct formatting and required fields."}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\n\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\nmessages = [\n    {\"role\": \"user\", \"content\": \"<user-message>\"},\n    {\"role\": \"assistant\", \"content\": \"<assistant-response>\"}\n]\n\nclient.add(messages, user_id=\"<user-id>\", version=\"v2\")"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst messages = [\n  { role: \"user\", content: \"Hi, I'm <PERSON>. I'm a vegetarian and I'm allergic to nuts.\" },\n  { role: \"assistant\", content: \"Hello <PERSON>! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions.\" }\n];\n\nclient.add(messages, { user_id: \"<user_id>\", version: \"v2\" })\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/v1/memories/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"messages\": [\n    {}\n  ],\n  \"agent_id\": \"<string>\",\n  \"user_id\": \"<string>\",\n  \"app_id\": \"<string>\",\n  \"run_id\": \"<string>\",\n  \"metadata\": {},\n  \"includes\": \"<string>\",\n  \"excludes\": \"<string>\",\n  \"infer\": true,\n  \"custom_categories\": {}, \n  \"org_id\": \"<string>\",\n  \"project_id\": \"<string>\",\n  \"version\": \"v2\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"messages\\\": [\n    {}\n  ],\n  \\\"agent_id\\\": \\\"<string>\\\",\n  \\\"user_id\\\": \\\"<string>\\\",\n  \\\"app_id\\\": \\\"<string>\\\",\n  \\\"run_id\\\": \\\"<string>\\\",\n  \\\"metadata\\\": {},\n  \\\"includes\\\": \\\"<string>\\\",\n  \\\"excludes\\\": \\\"<string>\\\",\n  \\\"infer\\\": true,\n  \\\"custom_categories\\\": {},\n  \\\"org_id\\\": \\\"<string>\\\",\n  \\\"project_id\\\": \\\"<string>\",\n  \\\"version\\\": \"v2\"\n}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"messages\\\": [\n    {}\n  ],\n  \\\"agent_id\\\": \\\"<string>\\\",\n  \\\"user_id\\\": \\\"<string>\\\",\n  \\\"app_id\\\": \\\"<string>\\\",\n  \\\"run_id\\\": \\\"<string>\\\",\n  \\\"metadata\\\": {},\n  \\\"includes\\\": \\\"<string>\\\",\n  \\\"excludes\\\": \\\"<string>\\\",\n  \\\"infer\\\": true,\n  \\\"custom_categories\\\": {}, \n  \\\"org_id\\\": \\\"<string>\\\",\n  \\\"project_id\\\": \\\"<string>\",\n  \\\"version\\\": \"v2\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/v1/memories/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n  \\\"messages\\\": [\n    {}\n  ],\n  \\\"agent_id\\\": \\\"<string>\\\",\n  \\\"user_id\\\": \\\"<string>\\\",\n  \\\"app_id\\\": \\\"<string>\\\",\n  \\\"run_id\\\": \\\"<string>\\\",\n  \\\"metadata\\\": {},\n  \\\"includes\\\": \\\"<string>\\\",\n  \\\"excludes\\\": \\\"<string>\\\",\n  \\\"infer\\\": true,\n  \\\"custom_categories\\\": {}, \n  \\\"org_id\\\": \\\"<string>\\\",\n  \\\"project_id\\\": \\\"<string>\",\n  \\\"version\\\": \"v2\"\n}\")\n  .asString();"}], "x-codegen-request-body-name": "data"}, "delete": {"tags": ["memories"], "description": "Delete memories", "operationId": "memories_delete", "parameters": [{"name": "user_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by user ID"}, {"name": "agent_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by agent ID"}, {"name": "app_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by app ID"}, {"name": "run_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by run ID"}, {"name": "metadata", "in": "query", "schema": {"type": "object"}, "description": "Filter memories by metadata (JSON string)", "style": "deepObject", "explode": true}, {"name": "org_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by organization ID."}, {"name": "project_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by project ID."}], "responses": {"204": {"description": "Successful deletion of memories", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Memories deleted successfully!"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\n# Delete all memories for a specific user\nclient.delete_all(user_id=\"<user_id>\")"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Delete all memories for a specific user\nclient.deleteAll({ user_id: \"<user_id>\" })\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request DELETE \\\n  --url https://api.mem0.ai/v1/memories/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/\"\n\n\treq, _ := http.NewRequest(\"DELETE\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/v1/memories/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}], "x-codegen-request-body-name": "data"}}, "/v2/memories/": {"post": {"tags": ["memories"], "description": "Get all memories", "operationId": "memories_list_v2", "parameters": [{"name": "filters", "in": "query", "schema": {"type": "object", "properties": {"user_id": {"type": "string"}, "agent_id": {"type": "string"}, "app_id": {"type": "string"}, "run_id": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "categories": {"type": "array", "items": {"type": "string"}}, "metadata": {"type": "object"}, "keywords": {"type": "string"}}, "additionalProperties": {"type": "object", "properties": {"in": {"type": "array"}, "gte": {"type": "string"}, "lte": {"type": "string"}, "gt": {"type": "string"}, "lt": {"type": "string"}, "ne": {"type": "string"}, "contains": {"type": "string"}, "icontains": {"type": "string"}}}}, "description": "Filters to apply to the memories. Available fields are: user_id, agent_id, app_id, run_id, created_at, updated_at, categories, keywords. Supports logical operators (AND, OR) and comparison operators (in, gte, lte, gt, lt, ne, contains, icontains)", "style": "deepObject", "explode": true}, {"name": "fields", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}, "description": "A list of field names to include in the response. If not provided, all fields will be returned."}, {"name": "org_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by organization ID."}, {"name": "project_id", "in": "query", "schema": {"type": "string"}, "description": "Filter memories by project ID."}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "description": "Page number for pagination. Default: 1"}, {"name": "page_size", "in": "query", "schema": {"type": "integer"}, "description": "Number of items per page. Default: 100"}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "memory": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "owner": {"type": "string"}, "immutable": {"type": "boolean", "description": "Whether the memory is immutable.", "title": "Immutable", "default": false}, "expiration_date": {"type": "string", "format": "date-time", "description": "The date and time when the memory will expire. Format: YYYY-MM-DD", "title": "Expiration date", "nullable": true, "default": null}, "organization": {"type": "string"}, "metadata": {"type": "object"}}, "required": ["id", "memory", "created_at", "updated_at", "total_memories", "owner", "organization", "type"]}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "One of the filters: app_id, user_id, agent_id, run_id is required!"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\n# Retrieve memories with filters\nmemories = client.get_all(\n    filters={\n        \"AND\": [\n            {\n                \"user_id\": \"alex\"\n            },\n            {\n                \"created_at\": {\n                    \"gte\": \"2024-07-01\",\n                    \"lte\": \"2024-07-31\"\n                }\n            }\n        ]\n    },\n    version=\"v2\"\n)\n\nprint(memories)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst filters = {\n  AND: [\n    { user_id: 'alex' },\n    { created_at: { gte: '2024-07-01', lte: '2024-07-31' } }\n  ]\n};\n\nclient.getAll({ filters, api_version: 'v2' })\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl -X POST 'https://api.mem0.ai/v2/memories/' \\\n-H 'Authorization: Token your-api-key' \\\n-H 'Content-Type: application/json' \\\n-d '{\n  \"filters\": {\n    \"AND\": [\n      { \"user_id\": \"alex\" },\n      { \"created_at\": { \"gte\": \"2024-07-01\", \"lte\": \"2024-07-31\" } }\n    ]\n  },\n  \"org_id\": \"your-org-id\",\n  \"project_id\": \"your-project-id\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"bytes\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"io/ioutil\"\n\t\"net/http\"\n)\n\nfunc main() {\n\turl := \"https://api.mem0.ai/v2/memories/\"\n\tfilters := map[string]interface{}{\n\t\t\"AND\": []map[string]interface{}{\n\t\t\t{\"user_id\": \"alex\"},\n\t\t\t{\"created_at\": map[string]string{\n\t\t\t\t\"gte\": \"2024-07-01\",\n\t\t\t\t\"lte\": \"2024-07-31\",\n\t\t\t}},\n\t\t},\n\t}\n\tpayload, _ := json.Marshal(map[string]interface{}{\"filters\": filters})\n\treq, _ := http.NewRequest(\"POST\", url, bytes.NewBuffer(payload))\n\treq.Header.Add(\"Authorization\", \"Token your-api-key\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(string(body))\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\n$filters = [\n  'AND' => [\n    ['user_id' => 'alex'],\n    ['created_at' => ['gte' => '2024-07-01', 'lte' => '2024-07-31']]\n  ]\n];\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v2/memories/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => json_encode(['filters' => $filters]),\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token your-api-key\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "import com.konghq.unirest.http.HttpResponse;\nimport com.konghq.unirest.http.Unirest;\nimport org.json.JSONObject;\n\nJSONObject filters = new JSONObject()\n    .put(\"AND\", new JSONArray()\n        .put(new JSONObject().put(\"user_id\", \"alex\"))\n        .put(new JSONObject().put(\"created_at\", new JSONObject()\n            .put(\"gte\", \"2024-07-01\")\n            .put(\"lte\", \"2024-07-31\")\n        ))\n    );\n\nHttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/v2/memories/\")\n  .header(\"Authorization\", \"Token your-api-key\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(new JSONObject().put(\"filters\", filters).toString())\n  .asString();\n\nSystem.out.println(response.getBody());"}]}}, "/v1/memories/events/": {"get": {"tags": ["memories"], "operationId": "memories_events_list", "responses": {"200": {"description": "", "content": {}}}}}, "/v1/memories/search/": {"post": {"tags": ["memories"], "description": "Perform a semantic search on memories.", "operationId": "memories_search_create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemorySearchInput"}}}, "required": true}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the memory"}, "memory": {"type": "string", "description": "The content of the memory"}, "user_id": {"type": "string", "description": "The identifier of the user associated with this memory"}, "metadata": {"type": "object", "nullable": true, "description": "Additional metadata associated with the memory"}, "categories": {"type": "array", "items": {"type": "string"}, "description": "Categories associated with the memory"}, "immutable": {"type": "boolean", "description": "Whether the memory is immutable.", "title": "Immutable", "default": false}, "expiration_date": {"type": "string", "format": "date-time", "description": "The date and time when the memory will expire. Format: YYYY-MM-DD", "title": "Expiration date", "nullable": true, "default": null}, "created_at": {"type": "string", "format": "date-time", "description": "The timestamp when the memory was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "The timestamp when the memory was last updated"}}, "required": ["id", "memory", "user_id", "created_at", "updated_at"]}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "At least one of the filters: agent_id, user_id, app_id, run_id is required!"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\nquery = \"Your search query here\"\n\nresults = client.search(query, user_id=\"<user_id>\", output_format=\"v1.1\")\nprint(results)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst query = \"Your search query here\";\n\nclient.search(query, { user_id: \"<user_id>\", output_format: \"v1.1\" })\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/v1/memories/search/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"query\": \"<string>\",\n  \"agent_id\": \"<string>\",\n  \"user_id\": \"<string>\",\n  \"app_id\": \"<string>\",\n  \"run_id\": \"<string>\",\n  \"metadata\": {},\n  \"top_k\": 123,\n  \"fields\": [\n    \"<string>\"\n  ],\n  \"rerank\": true,\n  \"org_id\": \"<string>\",\n  \"project_id\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/v1/memories/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}], "x-codegen-request-body-name": "data"}}, "/v2/memories/search/": {"post": {"tags": ["memories"], "description": "Search memories based on a query and filters.", "operationId": "memories_search_v2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemorySearchInputV2"}}}, "required": true}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the memory"}, "memory": {"type": "string", "description": "The content of the memory"}, "user_id": {"type": "string", "description": "The identifier of the user associated with this memory"}, "metadata": {"type": "object", "nullable": true, "description": "Additional metadata associated with the memory"}, "categories": {"type": "array", "items": {"type": "string"}, "description": "Categories associated with the memory"}, "immutable": {"type": "boolean", "description": "Whether the memory is immutable.", "title": "Immutable", "default": false}, "expiration_date": {"type": "string", "format": "date-time", "description": "The date and time when the memory will expire. Format: YYYY-MM-DD", "title": "Expiration date", "nullable": true, "default": null}, "created_at": {"type": "string", "format": "date-time", "description": "The timestamp when the memory was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "The timestamp when the memory was last updated"}}, "required": ["id", "memory", "user_id", "created_at", "updated_at"]}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\nquery = \"What do you know about me?\"\nfilters = {\n   \"OR\":[\n      {\n         \"user_id\":\"alex\"\n      },\n      {\n         \"agent_id\":{\n            \"in\":[\n               \"travel-assistant\",\n               \"customer-support\"\n            ]\n         }\n      }\n   ]\n}\nclient.search(query, version=\"v2\", filters=filters)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst query = \"What do you know about me?\";\nconst filters = {\n  OR: [\n    { user_id: \"alex\" },\n    { agent_id: { in: [\"travel-assistant\", \"customer-support\"] } }\n  ]\n};\n\nclient.search(query, { api_version: \"v2\", filters })\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/v2/memories/search/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"query\": \"<string>\",\n  \"filters\": {},\n  \"top_k\": 123,\n  \"fields\": [\n    \"<string>\"\n  ],\n  \"rerank\": true,\n  \"org_id\": \"<string>\",\n  \"project_id\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v2/memories/search/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"query\\\": \\\"<string>\\\",\n  \\\"filters\\\": {},\n  \\\"top_k\\\": 123,\n  \\\"fields\\\": [\n    \\\"<string>\\\"\n  ],\n  \\\"rerank\\\": true,\n  \\\"org_id\\\": \\\"<string>\\\",\n  \\\"project_id\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v2/memories/search/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"query\\\": \\\"<string>\\\",\n  \\\"filters\\\": {},\n  \\\"top_k\\\": 123,\n  \\\"fields\\\": [\n    \\\"<string>\\\"\n  ],\n  \\\"rerank\\\": true,\n  \\\"org_id\\\": \\\"<string>\\\",\n  \\\"project_id\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/v2/memories/search/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n  \\\"query\\\": \\\"<string>\\\",\n  \\\"filters\\\": {},\n  \\\"top_k\\\": 123,\n  \\\"fields\\\": [\n    \\\"<string>\\\"\n  ],\n  \\\"rerank\\\": true,\n  \\\"org_id\\\": \\\"<string>\\\",\n  \\\"project_id\\\": \\\"<string>\\\"\n}\")\n  .asString();"}], "x-codegen-request-body-name": "data"}}, "/v1/memories/{entity_type}/{entity_id}/": {"get": {"tags": ["memories"], "operationId": "memories_read", "responses": {"200": {"description": "", "content": {}}}}, "parameters": [{"name": "entity_type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string"}}]}, "/v1/memories/{memory_id}/": {"get": {"tags": ["memories"], "description": "Get a memory.", "operationId": "memories_read", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The unique identifier of the memory to retrieve"}], "responses": {"200": {"description": "Successfully retrieved the memory", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the memory"}, "memory": {"type": "string", "description": "The content of the memory"}, "user_id": {"type": "string", "description": "Identifier of the user associated with this memory"}, "agent_id": {"type": "string", "nullable": true, "description": "The agent ID associated with the memory, if any"}, "app_id": {"type": "string", "nullable": true, "description": "The app ID associated with the memory, if any"}, "run_id": {"type": "string", "nullable": true, "description": "The run ID associated with the memory, if any"}, "hash": {"type": "string", "description": "Hash of the memory content"}, "metadata": {"type": "object", "description": "Additional metadata associated with the memory"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the memory was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the memory was last updated"}}}}}}, "404": {"description": "Memory not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Memory not found!"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\nmemory = client.get(memory_id=\"<memory_id>\")"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Retrieve a specific memory\nclient.get(\"<memory_id>\")\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/v1/memories/{memory_id}/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/{memory_id}/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/{memory_id}/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/v1/memories/{memory_id}/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}, "put": {"tags": ["memories"], "description": "Get or Update or delete a memory.", "operationId": "memories_update", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The unique identifier of the memory to retrieve"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"text": {"type": "string", "description": "The updated text content of the memory"}, "metadata": {"type": "object", "description": "Additional metadata associated with the memory"}}}}}}, "responses": {"200": {"description": "Successfully updated memory", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The unique identifier of the updated memory"}, "text": {"type": "string", "description": "The updated text content of the memory"}, "user_id": {"type": "string", "nullable": true, "description": "The user ID associated with the memory, if any"}, "agent_id": {"type": "string", "nullable": true, "description": "The agent ID associated with the memory, if any"}, "app_id": {"type": "string", "nullable": true, "description": "The app ID associated with the memory, if any"}, "run_id": {"type": "string", "nullable": true, "description": "The run ID associated with the memory, if any"}, "hash": {"type": "string", "description": "Hash of the memory content"}, "metadata": {"type": "object", "description": "Additional metadata associated with the memory"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the memory was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the memory was last updated"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\n# Update a memory\nmemory_id = \"<memory_id>\"\nclient.update(\n    memory_id=memory_id,\n    text=\"Your updated memory message here\",\n    metadata={\"category\": \"example\"}\n)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Update a specific memory\nconst memory_id = \"<memory_id>\";\nclient.update(memory_id, { \n  text: \"Your updated memory message here\",\n  metadata: { category: \"example\" }\n})\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request PUT \\\n  --url https://api.mem0.ai/v1/memories/{memory_id}/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\"text\": \"Your updated memory text here\", \"metadata\": {\"category\": \"example\"}}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/{memory_id}/\"\n\n\tpayload := strings.NewReader(`{\n\t\"text\": \"Your updated memory text here\",\n\t\"metadata\": {\n\t\t\"category\": \"example\"\n\t}\n}`)\n\n\treq, _ := http.NewRequest(\"PUT\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/{memory_id}/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"PUT\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n  CURLOPT_POSTFIELDS => json_encode([\n    \"text\" => \"Your updated memory text here\",\n    \"metadata\" => [\"category\" => \"example\"]\n  ])\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.put(\"https://api.mem0.ai/v1/memories/{memory_id}/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\\\"text\\\": \\\"Your updated memory text here\\\", \\\"metadata\\\": {\\\"category\\\": \\\"example\\\"}}\")\n  .asString();"}], "x-codegen-request-body-name": "data"}, "delete": {"tags": ["memories"], "description": "Get or Update or delete a memory.", "operationId": "memories_delete", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The unique identifier of the memory to retrieve"}], "responses": {"204": {"description": "Successful deletion of memory", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Memory deleted successfully!"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\nmemory_id = \"<memory_id>\"\nclient.delete(memory_id=memory_id)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Delete a specific memory\nclient.delete(\"<memory_id>\")\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request DELETE \\\n  --url https://api.mem0.ai/v1/memories/{memory_id}/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/{memory_id}/\"\n\n\treq, _ := http.NewRequest(\"DELETE\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/{memory_id}/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/v1/memories/{memory_id}/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/v1/memories/{memory_id}/history/": {"get": {"tags": ["memories"], "description": "Retrieve the history of a memory.", "operationId": "memories_history_list", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "The unique identifier of the memory to retrieve"}], "responses": {"200": {"description": "Successfully retrieved the memory history", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the history entry"}, "memory_id": {"type": "string", "format": "uuid", "description": "Unique identifier of the associated memory"}, "input": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string", "enum": ["user", "assistant"], "description": "The role of the speaker in the conversation"}, "content": {"type": "string", "description": "The content of the message"}}, "required": ["role", "content"]}, "description": "The conversation input that led to this memory change"}, "old_memory": {"type": "string", "nullable": true, "description": "The previous state of the memory, if applicable"}, "new_memory": {"type": "string", "description": "The new or updated state of the memory"}, "user_id": {"type": "string", "description": "The identifier of the user associated with this memory"}, "event": {"type": "string", "enum": ["ADD", "UPDATE", "DELETE"], "description": "The type of event that occurred"}, "metadata": {"type": "object", "nullable": true, "description": "Additional metadata associated with the memory change"}, "created_at": {"type": "string", "format": "date-time", "description": "The timestamp when this history entry was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "The timestamp when this history entry was last updated"}}, "required": ["id", "memory_id", "input", "new_memory", "user_id", "event", "created_at", "updated_at"]}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\n# Add some message to create history\nmessages = [{\"role\": \"user\", \"content\": \"<user-message>\"}]\nclient.add(messages, user_id=\"<user-id>\")\n\n# Add second message to update history\nmessages.append({\"role\": \"user\", \"content\": \"<user-message>\"})\nclient.add(messages, user_id=\"<user-id>\")\n\n# Get history of how memory changed over time\nmemory_id = \"<memory-id-here>\"\nhistory = client.history(memory_id)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Get history of how memory changed over time\nclient.history(\"<memory_id>\")\n  .then(result => console.log(result))\n  .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/v1/memories/{memory_id}/history/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/v1/memories/{memory_id}/history/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/v1/memories/{memory_id}/history/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/v1/memories/{memory_id}/history/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/v1/runs/": {"post": {"tags": ["runs"], "description": "Create a new Agent Run.", "operationId": "runs_create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRun"}}}, "required": true}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRun"}}}}}, "x-codegen-request-body-name": "data"}}, "/v1/stats/": {"get": {"tags": ["stats"], "summary": "Retrieve memory-related statistics for the authenticated user.", "description": "This endpoint returns the following statistics:\n- Total number of memories created\n- Total number of search events\n- Total number of add events", "operationId": "stats_list", "responses": {"200": {"description": "", "content": {}}}}}, "/v1/users/": {"post": {"tags": ["users"], "description": "Create a new User.", "operationId": "users_create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUser"}}}, "required": true}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUser"}}}}}, "x-codegen-request-body-name": "data"}}, "/v1/feedback/": {"post": {"tags": ["feedback"], "description": "Submit feedback for a memory.", "operationId": "submit_feedback", "requestBody": {"content": {"application/json": {"schema": {"required": ["memory_id"], "type": "object", "properties": {"memory_id": {"type": "string", "description": "ID of the memory to provide feedback for"}, "feedback": {"type": "string", "enum": ["POSITIVE", "NEGATIVE", "VERY_NEGATIVE"], "nullable": true, "description": "Type of feedback"}, "feedback_reason": {"type": "string", "nullable": true, "description": "Reason for the feedback"}}}}}, "required": true}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Feedback ID"}, "feedback": {"type": "string", "enum": ["POSITIVE", "NEGATIVE", "VERY_NEGATIVE"], "nullable": true, "description": "Type of feedback"}, "feedback_reason": {"type": "string", "nullable": true, "description": "Reason for the feedback"}}}}}}, "400": {"description": "Invalid request"}, "401": {"description": "Unauthorized"}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\")\n\n# Submit feedback for a memory\nfeedback = client.feedback(memory_id=\"memory_id\", feedback=\"POSITIVE\")\nprint(feedback)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm install mem0ai\n\nimport MemoryClient from 'mem0ai';\n\nconst client = new MemoryClient({ apiKey: 'your-api-key'});\n\nclient.feedback({\n    memory_id: \"your-memory-id\", \n    feedback: \"NEGATIVE\", \n    feedback_reason: \"I don't like this memory because it is not relevant.\"\n})"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/v1/feedback/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\"memory_id\": \"memory_id\", \"feedback\": \"POSITIVE\"}'"}]}}, "/api/v1/orgs/organizations/": {"get": {"tags": ["organizations"], "operationId": "organizations_read", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the organization"}, "org_id": {"type": "string", "description": "Organization's unique string identifier"}, "name": {"type": "string", "description": "Name of the organization"}, "description": {"type": "string", "description": "Brief description of the organization"}, "address": {"type": "string", "description": "Physical address of the organization"}, "contact_email": {"type": "string", "description": "Primary contact email for the organization"}, "phone_number": {"type": "string", "description": "Contact phone number for the organization"}, "website": {"type": "string", "description": "Official website URL of the organization"}, "on_paid_plan": {"type": "boolean", "description": "Indicates whether the organization is on a paid plan"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the organization was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the organization was last updated"}, "owner": {"type": "integer", "description": "Identifier of the organization's owner"}, "members": {"type": "array", "items": {"type": "integer"}, "description": "List of member identifiers belonging to the organization"}}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'GET', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/api/v1/orgs/organizations/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}, "post": {"tags": ["organizations"], "description": "Create a new organization.", "operationId": "create_organization", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the new organization"}}, "required": ["name"]}}}}, "responses": {"201": {"description": "Successfully created a new organization", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization created successfully."}, "org_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the organization"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "description": "Errors found in the payload", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/\"\n\npayload = {\"name\": \"<string>\"}\nheaders = {\n    \"Authorization\": \"<api-key>\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {\n  method: 'POST',\n  headers: {Authorization: 'Token <api-key>', 'Content-Type': 'application/json'},\n  body: '{\"name\":\"<string>\"}'\n};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"name\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"name\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"name\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/api/v1/orgs/organizations/{org_id}/": {"get": {"tags": ["organizations"], "description": "Get a organization.", "operationId": "get_organization", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "The unique identifier of the organization", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the organization"}, "org_id": {"type": "string", "description": "Unique organization ID"}, "name": {"type": "string", "description": "Name of the organization"}, "description": {"type": "string", "description": "Description of the organization"}, "address": {"type": "string", "description": "Address of the organization"}, "contact_email": {"type": "string", "format": "email", "description": "Contact email for the organization"}, "phone_number": {"type": "string", "description": "Phone number of the organization"}, "website": {"type": "string", "format": "uri", "description": "Website of the organization"}, "on_paid_plan": {"type": "boolean", "description": "Indicates if the organization is on a paid plan"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the organization was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the organization was last updated"}, "owner": {"type": "integer", "description": "Identifier of the organization's owner"}, "members": {"type": "array", "items": {"type": "integer"}, "description": "List of member identifiers belonging to the organization"}}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\"\n\nheaders = {\"Authorization\": \"Token <api-key>\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'GET', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/api/v1/orgs/organizations/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\\n  \\\"name\\\": \\\"<string>\\\"\\n}\")\n  .asString();"}]}, "delete": {"tags": ["organizations"], "summary": "Delete an organization", "description": "Delete an organization by its ID.", "operationId": "delete_organization", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization to delete", "schema": {"type": "string"}}], "responses": {"200": {"description": "Organization deleted successfully!", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization deleted successfully!"}}}}}}, "403": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Unauthorized"}}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"DELETE\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'DELETE', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request DELETE \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/api/v1/orgs/organizations/{org_id}/members/": {"get": {"tags": ["organizations"], "summary": "Get organization members", "description": "Retrieve a list of members for a specific organization.", "operationId": "get_organization_members", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"members": {"type": "array", "items": {"type": "object", "properties": {"user_id": {"type": "string", "description": "Unique identifier of the member"}, "role": {"type": "string", "description": "Role of the member in the organization"}}}, "description": "List of members belonging to the organization"}}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'GET', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}, "put": {"tags": ["organizations"], "summary": "Update organization member role", "description": "Update the role of an existing member in a specific organization.", "operationId": "update_organization_member_role", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "role"], "properties": {"email": {"type": "string", "description": "Email of the member whose role is to be updated"}, "role": {"type": "string", "description": "New role of the member in the organization"}}}}}}, "responses": {"200": {"description": "User role updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "User role updated successfully"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "description": "Errors found in the payload", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\npayload = {\n    \"email\": \"<string>\",\n    \"role\": \"<string>\"\n}\nheaders = {\n    \"Authorization\": \"<api-key>\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.request(\"PUT\", url, json=payload, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {\n  method: 'PUT',\n  headers: {Authorization: 'Token <api-key>', 'Content-Type': 'application/json'},\n  body: '{\"email\":\"<string>\",\"role\":\"<string>\"}'\n};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request PUT \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"email\": \"<string>\",\n  \"role\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"PUT\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"PUT\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.put(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\")\n  .asString();"}]}, "post": {"tags": ["organizations"], "summary": "Add organization member", "description": "Add a new member to a specific organization.", "operationId": "add_organization_member", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "role"], "properties": {"email": {"type": "string", "description": "Email of the member to be added"}, "role": {"type": "string", "description": "Role of the member in the organization"}}}}}}, "responses": {"201": {"description": "Member added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "User added to the organization."}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "object", "description": "Errors found in the payload", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\npayload = {\n    \"email\": \"<string>\",\n    \"role\": \"<string>\"\n}\nheaders = {\n    \"Authorization\": \"<api-key>\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {\n  method: 'POST',\n  headers: {Authorization: 'Token <api-key>', 'Content-Type': 'application/json'},\n  body: '{\"email\":\"<string>\",\"role\":\"<string>\"}'\n};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"email\": \"<string>\",\n  \"role\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}, "delete": {"tags": ["organizations"], "summary": "Remove a member from the organization", "operationId": "remove_organization_member", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "description": "Email of the member to be removed"}}}}}}, "responses": {"200": {"description": "Member removed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "User removed from organization."}}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\npayload = {\"email\": \"<string>\"}\nheaders = {\n    \"Authorization\": \"<api-key>\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.request(\"DELETE\", url, json=payload, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {\n  method: 'DELETE',\n  headers: {Authorization: 'Token <api-key>', 'Content-Type': 'application/json'},\n  body: '{\"email\":\"<string>\"}'\n};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request DELETE \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"email\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"email\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"DELETE\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"email\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n  \\\"email\\\": \\\"<string>\\\"\n}\")\n  .asString();"}]}}, "/api/v1/orgs/organizations/{org_id}/projects/": {"get": {"tags": ["projects"], "summary": "Get projects", "description": "Retrieve a list of projects for a specific organization.", "operationId": "get_projects", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique numeric identifier of the project"}, "project_id": {"type": "string", "description": "Unique string identifier of the project"}, "name": {"type": "string", "description": "Name of the project"}, "description": {"type": "string", "description": "Description of the project"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the project was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the project was last updated"}, "members": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the project member"}, "role": {"type": "string", "description": "Role of the member in the project"}}}, "description": "List of members belonging to the project"}}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'GET', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}, "post": {"tags": ["projects"], "summary": "Create project", "description": "Create a new project within an organization.", "operationId": "create_project", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Name of the project to be created"}}}}}}, "responses": {"200": {"description": "Project created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Project created successfully."}, "project_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the project"}}}}}}, "403": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Unauthorized to create projects in this organization."}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Project could not be created."}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\"\n\npayload = {\"name\": \"<string>\"}\nheaders = {\n    \"Authorization\": \"<api-key>\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {\n  method: 'POST',\n  headers: {Authorization: 'Token <api-key>', 'Content-Type': 'application/json'},\n  body: '{\"name\":\"<string>\"}'\n};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"name\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"name\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"name\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n  \\\"name\\\": \\\"<string>\\\"\n}\")\n  .asString();"}]}}, "/api/v1/orgs/organizations/{org_id}/projects/{project_id}/": {"get": {"tags": ["projects"], "summary": "Get project details", "description": "Retrieve details of a specific project within an organization.", "operationId": "get_project", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}, {"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique numeric identifier of the project"}, "project_id": {"type": "string", "description": "Unique string identifier of the project"}, "name": {"type": "string", "description": "Name of the project"}, "description": {"type": "string", "description": "Description of the project"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the project was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp of when the project was last updated"}, "members": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string", "description": "<PERSON><PERSON><PERSON> of the project member"}, "role": {"type": "string", "description": "Role of the member in the project"}}}, "description": "List of members belonging to the project"}}}}}}, "404": {"description": "Organization or project not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization or project not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\n\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\nresponse = client.get_project()\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nclient.getProject()\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/mem0ai/mem0-go\n\npackage main\n\nimport (\n\t\"fmt\"\n\t\"github.com/mem0ai/mem0-go\"\n)\n\nfunc main() {\n\tclient := mem0.NewClient(\"your-api-key\")\n\n\tresponse, err := client.GetProject()\n\tif err != nil {\n\t\tfmt.Printf(\"Error: %v\\n\", err)\n\t\treturn\n\t}\n\tfmt.Printf(\"%+v\\n\", response)\n}"}, {"lang": "PHP", "source": "<?php\n// To use the PHP SDK, install the package:\n// composer require mem0ai/mem0-php\n\nrequire_once('vendor/autoload.php');\n\nuse Mem0\\MemoryClient;\n\n$client = new MemoryClient('your-api-key');\n\ntry {\n    $response = $client->getProject();\n    print_r($response);\n} catch (Exception $e) {\n    echo 'Error: ' . $e->getMessage();\n}"}, {"lang": "Java", "source": "// To use the Java SDK, add this dependency to your pom.xml:\n// <dependency>\n//     <groupId>ai.mem0</groupId>\n//     <artifactId>mem0-java</artifactId>\n//     <version>1.0.0</version>\n// </dependency>\n\nimport ai.mem0.MemoryClient;\n\npublic class Example {\n    public static void main(String[] args) {\n        MemoryClient client = new MemoryClient(\"your-api-key\");\n        \n        try {\n            Object response = client.getProject();\n            System.out.println(response);\n        } catch (Exception e) {\n            System.err.println(\"Error: \" + e.getMessage());\n        }\n    }\n}"}]}, "patch": {"tags": ["projects"], "summary": "Update Project", "description": "Update a specific project's settings.", "operationId": "update_project", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}, {"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project to be updated", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the project"}, "description": {"type": "string", "description": "Description of the project"}, "custom_instructions": {"type": "array", "items": {"type": "string"}, "description": "Custom instructions for memory processing in this project"}, "custom_categories": {"type": "array", "items": {"type": "object"}, "description": "List of custom categories to be used for memory categorization"}}}}}}, "responses": {"200": {"description": "Project updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Project updated successfully"}}}}}}, "404": {"description": "Organization or project not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization or project not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\n\nclient = MemoryClient(api_key=\"your_api_key\")\n\nnew_categories = [\n    {\"cooking\": \"For users interested in cooking and culinary experiences\"},\n    {\"fitness\": \"Includes content related to fitness and workouts\"}\n]\n\nresponse = client.update_project(custom_categories=new_categories)\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst newCategories = [\n    {\"cooking\": \"For users interested in cooking and culinary experiences\"},\n    {\"fitness\": \"Includes content related to fitness and workouts\"}\n];\n\nclient.updateProject({ custom_categories: newCategories })\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request PATCH \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n    \"custom_categories\": [\n      {\"cooking\": \"For users interested in cooking and culinary experiences\"},\n      {\"fitness\": \"Includes content related to fitness and workouts\"}\n    ]\n  }'"}, {"lang": "Go", "source": "// To use the Go SDK, install the package:\n// go get github.com/mem0ai/mem0-go\n\npackage main\n\nimport (\n\t\"fmt\"\n\t\"github.com/mem0ai/mem0-go\"\n)\n\nfunc main() {\n\tclient := mem0.NewClient(\"your-api-key\")\n\n\tnewCategories := []map[string]string{\n\t\t{\"cooking\": \"For users interested in cooking and culinary experiences\"},\n\t\t{\"fitness\": \"Includes content related to fitness and workouts\"},\n\t}\n\n\tresponse, err := client.UpdateProject(mem0.UpdateProjectParams{\n\t\tCustomCategories: newCategories,\n\t})\n\tif err != nil {\n\t\tfmt.Printf(\"Error: %v\\n\", err)\n\t\treturn\n\t}\n\tfmt.Printf(\"%+v\\n\", response)\n}"}, {"lang": "PHP", "source": "<?php\n// To use the PHP SDK, install the package:\n// composer require mem0ai/mem0-php\n\nrequire_once('vendor/autoload.php');\n\nuse Mem0\\MemoryClient;\n\n$client = new MemoryClient('your-api-key');\n\n$newCategories = [\n    ['cooking' => 'For users interested in cooking and culinary experiences'],\n    ['fitness' => 'Includes content related to fitness and workouts']\n];\n\ntry {\n    $response = $client->updateProject(['custom_categories' => $newCategories]);\n    print_r($response);\n} catch (Exception $e) {\n    echo 'Error: ' . $e->getMessage();\n}"}, {"lang": "Java", "source": "// To use the Java SDK, add this dependency to your pom.xml:\n// <dependency>\n//     <groupId>ai.mem0</groupId>\n//     <artifactId>mem0-java</artifactId>\n//     <version>1.0.0</version>\n// </dependency>\n\nimport ai.mem0.MemoryClient;\nimport java.util.*;\n\npublic class Example {\n    public static void main(String[] args) {\n        MemoryClient client = new MemoryClient(\"your-api-key\");\n        \n        List<Map<String, String>> newCategories = Arrays.asList(\n            Collections.singletonMap(\"cooking\", \"For users interested in cooking and culinary experiences\"),\n            Collections.singletonMap(\"fitness\", \"Includes content related to fitness and workouts\")\n        );\n        \n        try {\n            Map<String, Object> params = new HashMap<>();\n            params.put(\"custom_categories\", newCategories);\n            \n            Object response = client.updateProject(params);\n            System.out.println(response);\n        } catch (Exception e) {\n            System.err.println(\"Error: \" + e.getMessage());\n        }\n    }\n}"}]}, "delete": {"tags": ["projects"], "summary": "Delete Project", "description": "Delete a specific project and its related data.", "operationId": "delete_project", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}, {"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project to be deleted", "schema": {"type": "string"}}], "responses": {"200": {"description": "Project and related data deleted successfully.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Project and related data deleted successfully."}}}}}}, "404": {"description": "Organization or project not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization or project not found"}}}}}}, "403": {"description": "Unauthorized to modify this project", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Unauthorized to modify this project."}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"DELETE\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'DELETE', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request DELETE \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/\"\n\n\treq, _ := http.NewRequest(\"DELETE\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/": {"get": {"tags": ["projects"], "summary": "Get Project Members", "description": "Retrieve a list of members for a specific project.", "operationId": "get_project_members", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}, {"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved project members", "content": {"application/json": {"schema": {"type": "object", "properties": {"members": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "role": {"type": "string"}}}}}}}}}, "404": {"description": "Organization or project not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization or project not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"GET\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'GET', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request GET \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\n\treq, _ := http.NewRequest(\"GET\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"GET\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}, "post": {"tags": ["projects"], "summary": "Add member to project", "description": "Add a new member to a specific project within an organization.", "operationId": "add_project_member", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}, {"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "role"], "properties": {"email": {"type": "string", "description": "Email of the member to be added"}, "role": {"type": "string", "description": "Role of the member in the project"}}}}}}, "responses": {"200": {"description": "User added to the project successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "User added to the project successfully."}}}}}}, "403": {"description": "Unauthorized to modify project members", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Unauthorized to modify project members."}}}}}}, "404": {"description": "Organization or project not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization or project not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\npayload = {\n    \"email\": \"<string>\",\n    \"role\": \"<string>\"\n}\nheaders = {\n    \"Authorization\": \"<api-key>\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.request(\"POST\", url, json=payload, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {\n  method: 'POST',\n  headers: {Authorization: 'Token <api-key>', 'Content-Type': 'application/json'},\n  body: '{\"email\":\"<string>\",\"role\":\"<string>\"}'\n};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request POST \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"email\": \"<string>\",\n  \"role\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"POST\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"POST\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\")\n  .asString();"}]}, "put": {"tags": ["projects"], "summary": "Update project member role", "description": "Update the role of a member in a specific project within an organization.", "operationId": "update_project_member", "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}, {"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "role"], "properties": {"email": {"type": "string", "description": "Email of the member to be updated"}, "role": {"type": "string", "description": "New role of the member in the project"}}}}}}, "responses": {"200": {"description": "User role updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "User role updated successfully."}}}}}}, "403": {"description": "Unauthorized to modify project members", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Unauthorized to modify project members."}}}}}}, "404": {"description": "Organization or project not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization or project not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\npayload = {\n    \"email\": \"<string>\",\n    \"role\": \"<string>\"\n}\nheaders = {\n    \"Authorization\": \"<api-key>\",\n    \"Content-Type\": \"application/json\"\n}\n\nresponse = requests.request(\"PUT\", url, json=payload, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {\n  method: 'PUT',\n  headers: {Authorization: 'Token <api-key>', 'Content-Type': 'application/json'},\n  body: '{\"email\":\"<string>\",\"role\":\"<string>\"}'\n};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request PUT \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/ \\\n  --header 'Authorization: Token <api-key>' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n  \"email\": \"<string>\",\n  \"role\": \"<string>\"\n}'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\n\tpayload := strings.NewReader(\"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\")\n\n\treq, _ := http.NewRequest(\"PUT\", url, payload)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"PUT\",\n  CURLOPT_POSTFIELDS => \"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.put(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n  \\\"email\\\": \\\"<string>\\\",\n  \\\"role\\\": \\\"<string>\\\"\n}\")\n  .asString();"}]}, "delete": {"summary": "Delete Project Member", "operationId": "deleteProjectMember", "tags": ["Project"], "parameters": [{"name": "org_id", "in": "path", "required": true, "description": "Unique identifier of the organization", "schema": {"type": "string"}}, {"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project", "schema": {"type": "string"}}, {"name": "email", "in": "query", "required": true, "description": "Email of the member to be removed", "schema": {"type": "string"}}], "responses": {"200": {"description": "Member removed from the project successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Member removed from the project"}}}}}}, "403": {"description": "Unauthorized to modify project members", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Unauthorized to modify project members."}}}}}}, "404": {"description": "Organization or project not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Organization or project not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "import requests\n\nurl = \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\nheaders = {\"Authorization\": \"<api-key>\"}\n\nresponse = requests.request(\"DELETE\", url, headers=headers)\n\nprint(response.text)"}, {"lang": "JavaScript", "source": "const options = {method: 'DELETE', headers: {Authorization: 'Token <api-key>'}};\n\nfetch('https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/', options)\n  .then(response => response.json())\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "curl --request DELETE \\\n  --url https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/ \\\n  --header 'Authorization: Token <api-key>'"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\n\turl := \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\"\n\n\treq, _ := http.NewRequest(\"DELETE\", url, nil)\n\n\treq.Header.Add(\"Authorization\", \"Token <api-key>\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\n\tfmt.Println(res)\n\tfmt.Println(string(body))\n\n}"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_ENCODING => \"\",\n  CURLOPT_MAXREDIRS => 10,\n  CURLOPT_TIMEOUT => 30,\n  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token <api-key>\"\n  ],\n]);\n\n$response = curl_exec($curl);\n$err = curl_error($curl);\n\ncurl_close($curl);\n\nif ($err) {\n  echo \"cURL Error #:\" . $err;\n} else {\n  echo $response;\n}"}, {"lang": "Java", "source": "HttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/api/v1/orgs/organizations/{org_id}/projects/{project_id}/members/\")\n  .header(\"Authorization\", \"Token <api-key>\")\n  .asString();"}]}}, "/v1/batch/": {"put": {"tags": ["memories"], "description": "Batch update multiple memories (up to 1000) in a single API call.", "operationId": "memories_batch_update", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"memories": {"type": "array", "items": {"type": "object", "required": ["memory_id", "text"], "properties": {"memory_id": {"type": "string", "format": "uuid", "description": "The unique identifier of the memory to update"}, "text": {"type": "string", "description": "The new text content for the memory"}}}, "maxItems": 1000}}, "required": ["memories"]}}}, "required": true}, "responses": {"200": {"description": "Successfully updated memories", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Successfully updated 2 memories"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Maximum of 1000 memories can be updated in a single request"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\nupdate_memories = [\n    {\n        \"memory_id\": \"285ed74b-6e05-4043-b16b-3abd5b533496\",\n        \"text\": \"Watches football\"\n    },\n    {\n        \"memory_id\": \"2c9bd859-d1b7-4d33-a6b8-94e0147c4f07\",\n        \"text\": \"Likes to travel\"\n    }\n]\n\nresponse = client.batch_update(update_memories)\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst updateMemories = [\n    {\n        memoryId: \"285ed74b-6e05-4043-b16b-3abd5b533496\",\n        text: \"Watches football\"\n    },\n    {\n        memoryId: \"2c9bd859-d1b7-4d33-a6b8-94e0147c4f07\",\n        text: \"Likes to travel\"\n    }\n];\n\nclient.batchUpdate(updateMemories)\n    .then(response => console.log('Batch update response:', response))\n    .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl -X PUT \"https://api.mem0.ai/v1/batch/\" \\\n     -H \"Authorization: Token your-api-key\" \\\n     -H \"Content-Type: application/json\" \\\n     -d '{\n         \"memories\": [\n             {\n                 \"memory_id\": \"285ed74b-6e05-4043-b16b-3abd5b533496\",\n                 \"text\": \"Watches football\"\n             },\n             {\n                 \"memory_id\": \"2c9bd859-d1b7-4d33-a6b8-94e0147c4f07\",\n                 \"text\": \"Likes to travel\"\n             }\n         ]\n     }'"}]}, "delete": {"tags": ["memories"], "description": "Batch delete multiple memories (up to 1000) in a single API call.", "operationId": "memories_batch_delete", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"memory_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "maxItems": 1000, "description": "Array of memory IDs to delete"}}, "required": ["memory_ids"]}}}, "required": true}, "responses": {"200": {"description": "Successfully deleted memories", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Successfully deleted 2 memories"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Maximum of 1000 memories can be deleted in a single request"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\ndelete_memories = [\n    {\"memory_id\": \"285ed74b-6e05-4043-b16b-3abd5b533496\"},\n    {\"memory_id\": \"2c9bd859-d1b7-4d33-a6b8-94e0147c4f07\"}\n]\n\nresponse = client.batch_delete(delete_memories)\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\nconst deleteMemories = [\n    { memory_id: \"285ed74b-6e05-4043-b16b-3abd5b533496\" },\n    { memory_id: \"2c9bd859-d1b7-4d33-a6b8-94e0147c4f07\" }\n];\n\nclient.batchDelete(deleteMemories)\n    .then(response => console.log('Batch delete response:', response))\n    .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl -X DELETE \"https://api.mem0.ai/v1/batch/\" \\\n     -H \"Authorization: Token your-api-key\" \\\n     -H \"Content-Type: application/json\" \\\n     -d '{\n         \"memories\": [\n             {\n                 \"memory_id\": \"285ed74b-6e05-4043-b16b-3abd5b533496\"\n             },\n             {\n                 \"memory_id\": \"2c9bd859-d1b7-4d33-a6b8-94e0147c4f07\"\n             }\n         ]\n     }'"}]}}, "/api/v1/webhooks/projects/{project_id}/": {"get": {"tags": ["webhooks"], "summary": "Get Project Webhooks", "description": "Retrieve all webhooks for a specific project", "operationId": "get_project_webhooks", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of webhooks for the project", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"webhook_id": {"type": "string", "description": "Unique identifier of the webhook"}, "name": {"type": "string", "description": "Name of the webhook"}, "url": {"type": "string", "description": "URL endpoint for the webhook"}, "event_types": {"type": "array", "items": {"type": "string"}, "description": "List of event types the webhook subscribes to"}, "is_active": {"type": "boolean", "description": "Whether the webhook is active"}, "project": {"type": "string", "description": "Name of the project the webhook is associated with"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp when the webhook was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "Timestamp when the webhook was last updated"}}}}}}}, "403": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "You don't have access to this project"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\")\n\n# Get all webhooks\nwebhooks = client.get_webhooks(project_id=\"your_project_id\")\nprint(webhooks)\n\n# Create a webhook\nwebhook = client.create_webhook(\n    url=\"https://your-webhook-url.com\",\n    name=\"My Webhook\",\n    project_id=\"your_project_id\",\n    event_types=[\"memory:add\"]\n)\nprint(webhook)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: 'your-api-key' });\n\n// Get all webhooks\nclient.getWebhooks('your_project_id')\n  .then(webhooks => console.log(webhooks))\n  .catch(err => console.error(err));\n\n// Create a webhook\nclient.createWebhook({\n  url: 'https://your-webhook-url.com',\n  name: 'My Webhook',\n  project_id: 'your_project_id',\n  event_types: ['memory:add']\n})\n  .then(webhook => console.log(webhook))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "# Get all webhooks\ncurl --request GET \\\n  --url 'https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/' \\\n  --header 'Authorization: Token your-api-key'\n\n# Create a webhook\ncurl --request POST \\\n  --url 'https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/' \\\n  --header 'Authorization: Token your-api-key' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n    \"url\": \"https://your-webhook-url.com\",\n    \"name\": \"My Webhook\",\n    \"event_types\": [\"memory:add\"]\n  }'"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\n// Get all webhooks\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_HTTPHEADER => [\"Authorization: Token your-api-key\"],\n]);\n\n$response = curl_exec($curl);\n\n// Create a webhook\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_POST => true,\n  CURLOPT_POSTFIELDS => json_encode([\n    \"url\" => \"https://your-webhook-url.com\",\n    \"name\" => \"My Webhook\",\n    \"event_types\" => [\"memory:add\"]\n  ]),\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token your-api-key\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\ncurl_close($curl);"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\t// Get all webhooks\n\treq, _ := http.NewRequest(\"GET\", \"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\", nil)\n\treq.Header.Add(\"Authorization\", \"Token your-api-key\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\tfmt.Println(string(body))\n\n\t// Create a webhook\n\tpayload := strings.NewReader(`{\n\t\t\"url\": \"https://your-webhook-url.com\",\n\t\t\"name\": \"My Webhook\",\n\t\t\"event_types\": [\"memory:add\"]\n\t}`)\n\n\treq, _ = http.NewRequest(\"POST\", \"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\", payload)\n\treq.Header.Add(\"Authorization\", \"Token your-api-key\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ = http.DefaultClient.Do(req)\n\tdefer res.Body.Close()\n\tbody, _ = ioutil.ReadAll(res.Body)\n\tfmt.Println(string(body))\n}"}, {"lang": "Java", "source": "// Get all webhooks\nHttpResponse<String> response = Unirest.get(\"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\")\n  .header(\"Authorization\", \"Token your-api-key\")\n  .asString();\n\n// Create a webhook\nHttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\")\n  .header(\"Authorization\", \"Token your-api-key\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n    \\\"url\\\": \\\"https://your-webhook-url.com\\\",\n    \\\"name\\\": \\\"My Webhook\\\",\n    \\\"event_types\\\": [\\\"memory:add\\\"]\n  }\")\n  .asString();"}]}, "post": {"tags": ["webhooks"], "summary": "Create Webhook", "description": "Create a new webhook for a specific project", "operationId": "create_webhook", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "Unique identifier of the project", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["url"], "properties": {"name": {"type": "string", "description": "Name of the webhook"}, "url": {"type": "string", "description": "URL endpoint for the webhook"}, "event_types": {"type": "array", "items": {"type": "string", "enum": ["memory:add", "memory:update", "memory:delete"]}, "description": "List of event types to subscribe to"}, "is_active": {"type": "boolean", "description": "Whether the webhook is active"}, "project_id": {"type": "string", "description": "Unique identifier of the project"}}}}}}, "responses": {"201": {"description": "Webhook created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"webhook_id": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}, "event_types": {"type": "array", "items": {"type": "string"}}, "is_active": {"type": "boolean"}, "project": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "403": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "You don't have access to this project"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\n# Create a webhook\nwebhook = client.create_webhook(\n    url=\"https://your-webhook-url.com\",\n    name=\"My Webhook\",\n    project_id=\"your_project_id\",\n    event_types=[\"memory:add\"]\n)\nprint(webhook)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Create a webhook\nclient.createWebhook({\n    url: \"https://your-webhook-url.com\",\n    name: \"My Webhook\",\n    project_id: \"your_project_id\",\n    event_types: [\"memory:add\"]\n})\n    .then(response => console.log('Create webhook response:', response))\n    .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl -X POST \"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\" \\\n     -H \"Authorization: Token your-api-key\" \\\n     -H \"Content-Type: application/json\" \\\n     -d '{\n         \"url\": \"https://your-webhook-url.com\",\n         \"name\": \"My Webhook\",\n         \"event_types\": [\"memory:add\"]\n     }'"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n    CURLOPT_URL => \"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\",\n    CURLOPT_RETURNTRANSFER => true,\n    CURLOPT_POST => true,\n    CURLOPT_POSTFIELDS => json_encode([\n        \"url\" => \"https://your-webhook-url.com\",\n        \"name\" => \"My Webhook\",\n        \"event_types\" => [\"memory:add\"]\n    ]),\n    CURLOPT_HTTPHEADER => [\n        \"Authorization: Token your-api-key\",\n        \"Content-Type: application/json\"\n    ],\n]);\n\n$response = curl_exec($curl);\ncurl_close($curl);\n\necho $response;"}, {"lang": "Go", "source": "package main\n\nimport (\n    \"fmt\"\n    \"strings\"\n    \"net/http\"\n    \"io/ioutil\"\n)\n\nfunc main() {\n    payload := strings.NewReader(`{\n        \"url\": \"https://your-webhook-url.com\",\n        \"name\": \"My Webhook\",\n        \"event_types\": [\"memory:add\"]\n    }`)\n\n    req, _ := http.NewRequest(\"POST\", \"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\", payload)\n    req.Header.Add(\"Authorization\", \"Token your-api-key\")\n    req.Header.Add(\"Content-Type\", \"application/json\")\n\n    res, _ := http.DefaultClient.Do(req)\n    defer res.Body.Close()\n    body, _ := ioutil.ReadAll(res.Body)\n\n    fmt.Println(string(body))\n}"}, {"lang": "Java", "source": "import com.konghq.unirest.http.HttpResponse;\nimport com.konghq.unirest.http.Unirest;\n\n// Create a webhook\nHttpResponse<String> response = Unirest.post(\"https://api.mem0.ai/api/v1/webhooks/your_project_id/webhook/\")\n    .header(\"Authorization\", \"Token your-api-key\")\n    .header(\"Content-Type\", \"application/json\")\n    .body(\"{\n        \\\"url\\\": \\\"https://your-webhook-url.com\\\",\n        \\\"name\\\": \\\"My Webhook\\\",\n        \\\"event_types\\\": [\\\"memory:add\\\"]\n    }\")\n    .asString();\n\nSystem.out.println(response.getBody());"}]}}, "/api/v1/webhooks/{webhook_id}/": {"put": {"tags": ["webhooks"], "summary": "Update Webhook", "description": "Update an existing webhook", "operationId": "update_webhook", "parameters": [{"name": "webhook_id", "in": "path", "required": true, "description": "Unique identifier of the webhook", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "New name for the webhook"}, "url": {"type": "string", "description": "New URL endpoint for the webhook"}, "event_types": {"type": "array", "items": {"type": "string", "enum": ["memory:add", "memory:update", "memory:delete"]}, "description": "New list of event types to subscribe to"}}}}}}, "responses": {"200": {"description": "Webhook updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Webhook updated successfully"}}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "403": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "You don't have access to this webhook"}}}}}}, "404": {"description": "Webhook not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Webhook not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\")\n\n# Update a webhook\nwebhook = client.update_webhook(\n    webhook_id=\"your_webhook_id\",\n    name=\"Updated Webhook\",\n    url=\"https://new-webhook-url.com\",\n    event_types=[\"memory:add\"]\n)\nprint(webhook)\n\n# Delete a webhook\nresponse = client.delete_webhook(webhook_id=\"your_webhook_id\")\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: 'your-api-key' });\n\n// Update a webhook\nclient.updateWebhook('your_webhook_id', {\n  name: 'Updated Webhook',\n  url: 'https://new-webhook-url.com',\n  event_types: ['memory:add']\n})\n  .then(webhook => console.log(webhook))\n  .catch(err => console.error(err));\n\n// Delete a webhook\nclient.deleteWebhook('your_webhook_id')\n  .then(response => console.log(response))\n  .catch(err => console.error(err));"}, {"lang": "cURL", "source": "# Update a webhook\ncurl --request PUT \\\n  --url 'https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/' \\\n  --header 'Authorization: Token your-api-key' \\\n  --header 'Content-Type: application/json' \\\n  --data '{\n    \"name\": \"Updated Webhook\",\n    \"url\": \"https://new-webhook-url.com\",\n    \"event_types\": [\"memory:add\"]\n  }'\n\n# Delete a webhook\ncurl --request DELETE \\\n  --url 'https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/' \\\n  --header 'Authorization: Token your-api-key'"}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\n// Update a webhook\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_CUSTOMREQUEST => \"PUT\",\n  CURLOPT_POSTFIELDS => json_encode([\n    \"name\" => \"Updated Webhook\",\n    \"url\" => \"https://new-webhook-url.com\",\n    \"event_types\" => [\"memory:add\"]\n  ]),\n  CURLOPT_HTTPHEADER => [\n    \"Authorization: Token your-api-key\",\n    \"Content-Type: application/json\"\n  ],\n]);\n\n$response = curl_exec($curl);\n\n// Delete a webhook\ncurl_setopt_array($curl, [\n  CURLOPT_URL => \"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\",\n  CURLOPT_RETURNTRANSFER => true,\n  CURLOPT_CUSTOMREQUEST => \"DELETE\",\n  CURLOPT_HTTPHEADER => [\"Authorization: Token your-api-key\"],\n]);\n\n$response = curl_exec($curl);\ncurl_close($curl);"}, {"lang": "Go", "source": "package main\n\nimport (\n\t\"fmt\"\n\t\"strings\"\n\t\"net/http\"\n\t\"io/ioutil\"\n)\n\nfunc main() {\n\t// Update a webhook\n\tpayload := strings.NewReader(`{\n\t\t\"name\": \"Updated Webhook\",\n\t\t\"url\": \"https://new-webhook-url.com\",\n\t\t\"event_types\": [\"memory:add\"]\n\t}`)\n\n\treq, _ := http.NewRequest(\"PUT\", \"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\", payload)\n\treq.Header.Add(\"Authorization\", \"Token your-api-key\")\n\treq.Header.Add(\"Content-Type\", \"application/json\")\n\n\tres, _ := http.DefaultClient.Do(req)\n\tdefer res.Body.Close()\n\tbody, _ := ioutil.ReadAll(res.Body)\n\tfmt.Println(string(body))\n\n\t// Delete a webhook\n\treq, _ = http.NewRequest(\"DELETE\", \"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\", nil)\n\treq.Header.Add(\"Authorization\", \"Token your-api-key\")\n\n\tres, _ = http.DefaultClient.Do(req)\n\tdefer res.Body.Close()\n\tbody, _ = ioutil.ReadAll(res.Body)\n\tfmt.Println(string(body))\n}"}, {"lang": "Java", "source": "// Update a webhook\nHttpResponse<String> response = Unirest.put(\"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\")\n  .header(\"Authorization\", \"Token your-api-key\")\n  .header(\"Content-Type\", \"application/json\")\n  .body(\"{\n    \\\"name\\\": \\\"Updated Webhook\\\",\n    \\\"url\\\": \\\"https://new-webhook-url.com\\\",\n    \\\"event_types\\\": [\\\"memory:add\\\"]\n  }\")\n  .asString();\n\n// Delete a webhook\nHttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\")\n  .header(\"Authorization\", \"Token your-api-key\")\n  .asString();"}]}, "delete": {"tags": ["webhooks"], "summary": "Delete Webhook", "description": "Delete an existing webhook", "operationId": "delete_webhook", "parameters": [{"name": "webhook_id", "in": "path", "required": true, "description": "Unique identifier of the webhook", "schema": {"type": "string"}}], "responses": {"200": {"description": "Webhook deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Webhook deleted successfully"}}}}}}, "403": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "You don't have access to this webhook"}}}}}}, "404": {"description": "Webhook not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Webhook not found"}}}}}}}, "x-code-samples": [{"lang": "Python", "source": "# To use the Python SDK, install the package:\n# pip install mem0ai\n\nfrom mem0 import MemoryClient\nclient = MemoryClient(api_key=\"your_api_key\", org_id=\"your_org_id\", project_id=\"your_project_id\")\n\n# Delete a webhook\nresponse = client.delete_webhook(webhook_id=\"your_webhook_id\")\nprint(response)"}, {"lang": "JavaScript", "source": "// To use the JavaScript SDK, install the package:\n// npm i mem0ai\n\nimport MemoryClient from 'mem0ai';\nconst client = new MemoryClient({ apiKey: \"your-api-key\" });\n\n// Delete a webhook\nclient.deleteWebhook(\"your_webhook_id\")\n    .then(response => console.log('Delete webhook response:', response))\n    .catch(error => console.error(error));"}, {"lang": "cURL", "source": "curl -X DELETE \"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\" \\\n     -H \"Authorization: Token your-api-key\""}, {"lang": "PHP", "source": "<?php\n\n$curl = curl_init();\n\ncurl_setopt_array($curl, [\n    CURLOPT_URL => \"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\",\n    CURLOPT_RETURNTRANSFER => true,\n    CURLOPT_CUSTOMREQUEST => \"DELETE\",\n    CURLOPT_HTTPHEADER => [\"Authorization: Token your-api-key\"],\n]);\n\n$response = curl_exec($curl);\ncurl_close($curl);\n\necho $response;"}, {"lang": "Go", "source": "package main\n\nimport (\n    \"fmt\"\n    \"net/http\"\n    \"io/ioutil\"\n)\n\nfunc main() {\n    req, _ := http.NewRequest(\"DELETE\", \"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\", nil)\n    req.Header.Add(\"Authorization\", \"Token your-api-key\")\n\n    res, _ := http.DefaultClient.Do(req)\n    defer res.Body.Close()\n    body, _ := ioutil.ReadAll(res.Body)\n\n    fmt.Println(string(body))\n}"}, {"lang": "Java", "source": "import com.konghq.unirest.http.HttpResponse;\nimport com.konghq.unirest.http.Unirest;\n\n// Delete a webhook\nHttpResponse<String> response = Unirest.delete(\"https://api.mem0.ai/api/v1/webhooks/your_webhook_id/webhook/\")\n    .header(\"Authorization\", \"Token your-api-key\")\n    .asString();\n\nSystem.out.println(response.getBody());"}]}}}, "components": {"schemas": {"CreateAgent": {"required": ["agent_id"], "type": "object", "properties": {"agent_id": {"title": "Agent id", "minLength": 1, "type": "string"}, "name": {"title": "Name", "minLength": 1, "type": "string"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {}}}}, "CreateApp": {"required": ["app_id"], "type": "object", "properties": {"app_id": {"title": "App id", "minLength": 1, "type": "string"}, "name": {"title": "Name", "minLength": 1, "type": "string"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {}}}}, "MemoryInput": {"type": "object", "properties": {"messages": {"description": "An array of message objects representing the content of the memory. Each message object typically contains 'role' and 'content' fields, where 'role' indicates the sender either 'user' or 'assistant' and 'content' contains the actual message text. This structure allows for the representation of conversations or multi-part memories.", "type": "array", "items": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}}}, "agent_id": {"description": "The unique identifier of the agent associated with this memory.", "title": "Agent id", "type": "string", "nullable": true}, "user_id": {"description": "The unique identifier of the user associated with this memory.", "title": "User id", "type": "string", "nullable": true}, "app_id": {"description": "The unique identifier of the application associated with this memory.", "title": "App id", "type": "string", "nullable": true}, "run_id": {"description": "The unique identifier of the run associated with this memory.", "title": "Run id", "type": "string", "nullable": true}, "metadata": {"description": "Additional metadata associated with the memory, which can be used to store any additional information or context about the memory. Best practice for incorporating additional information is through metadata (e.g. location, time, ids, etc.). During retrieval, you can either use these metadata alongside the query to fetch relevant memories or retrieve memories based on the query first and then refine the results using metadata during post-processing.", "title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {}, "nullable": true}, "includes": {"description": "String to include the specific preferences in the memory.", "title": "Includes", "minLength": 1, "type": "string", "nullable": true}, "excludes": {"description": "String to exclude the specific preferences in the memory.", "title": "Excludes", "minLength": 1, "type": "string", "nullable": true}, "infer": {"description": "Wether to infer the memories or directly store the messages.", "title": "Infer", "type": "boolean", "default": true}, "output_format": {"description": "It two output formats: `v1.0` (default) and `v1.1`. We recommend using `v1.1` as `v1.0` will be deprecated soon.", "title": "Output format", "type": "string", "nullable": true, "default": "v1.0"}, "custom_categories": {"description": "A list of categories with category name and it's description.", "title": "Custom categories", "type": "object", "properties": {}, "nullable": true}, "custom_instructions": {"description": "Defines project-specific guidelines for handling and organizing memories. When set at the project level, they apply to all new memories in that project.", "title": "Custom instructions", "type": "string", "nullable": true}, "immutable": {"description": "Whether the memory is immutable.", "title": "Immutable", "type": "boolean", "default": false}, "async_mode": {"description": "Whether to add the memory completely asynchronously.", "title": "Async mode", "type": "boolean", "default": false}, "timestamp": {"description": "The timestamp of the memory. Format: Unix timestamp", "title": "Timestamp", "type": "integer", "nullable": true}, "expiration_date": {"description": "The date and time when the memory will expire. Format: YYYY-MM-DD", "title": "Expiration date", "type": "string", "nullable": true}, "org_id": {"description": "The unique identifier of the organization associated with this memory.", "title": "Organization id", "type": "string", "nullable": true}, "project_id": {"description": "The unique identifier of the project associated with this memory.", "title": "Project id", "type": "string", "nullable": true}, "version": {"description": "The version of the memory to use. The default version is v1, which is deprecated. We recommend using v2 for new applications.", "title": "Version", "type": "string", "nullable": true}}}, "MemorySearchInput": {"required": ["query"], "type": "object", "properties": {"query": {"title": "Query", "minLength": 1, "type": "string", "description": "The query to search for in the memory."}, "agent_id": {"title": "Agent id", "type": "string", "nullable": true, "description": "The agent ID associated with the memory."}, "user_id": {"title": "User id", "type": "string", "nullable": true, "description": "The user ID associated with the memory."}, "app_id": {"title": "App id", "type": "string", "nullable": true, "description": "The app ID associated with the memory."}, "run_id": {"title": "Run id", "type": "string", "nullable": true, "description": "The run ID associated with the memory."}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {}, "nullable": true, "description": "Additional metadata associated with the memory."}, "top_k": {"title": "Top K", "type": "integer", "default": 10, "description": "The number of top results to return."}, "fields": {"title": "Fields", "type": "array", "items": {"type": "string"}, "description": "A list of field names to include in the response. If not provided, all fields will be returned."}, "rerank": {"title": "<PERSON><PERSON>", "type": "boolean", "default": false, "description": "Whether to rerank the memories."}, "keyword_search": {"title": "Keyword search", "type": "boolean", "default": false, "description": "Whether to search for memories based on keywords."}, "output_format": {"title": "Output format", "type": "string", "nullable": true, "default": "v1.0", "description": "The search method supports two output formats: `v1.0` (default) and `v1.1`. We recommend using `v1.1` as `v1.0` will be deprecated soon."}, "org_id": {"title": "Organization id", "type": "string", "nullable": true, "description": "The unique identifier of the organization associated with the memory."}, "project_id": {"title": "Project id", "type": "string", "nullable": true, "description": "The unique identifier of the project associated with the memory."}, "filter_memories": {"title": "Filter memories", "type": "boolean", "default": false, "description": "Whether to properly filter the memories according to the input."}, "categories": {"title": "Categories", "type": "array", "items": {"type": "string"}, "description": "A list of categories to filter the memories by."}, "only_metadata_based_search": {"title": "Only metadata based search", "type": "boolean", "default": false, "description": "Whether to only search for memories based on metadata."}}}, "MemorySearchInputV2": {"type": "object", "required": ["query", "filters"], "properties": {"query": {"title": "Query", "type": "string", "description": "The query to search for in the memory."}, "filters": {"title": "Filters", "type": "object", "description": "A dictionary of filters to apply to the search. Available fields are: user_id, agent_id, app_id, run_id, created_at, updated_at, categories, keywords. Supports logical operators (AND, OR) and comparison operators (in, gte, lte, gt, lt, ne, contains, icontains).", "properties": {"user_id": {"type": "string"}, "agent_id": {"type": "string"}, "app_id": {"type": "string"}, "run_id": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "text": {"type": "string"}, "categories": {"type": "array", "items": {"type": "string"}}, "metadata": {"type": "object"}}, "additionalProperties": {"type": "object", "properties": {"in": {"type": "array"}, "gte": {"type": "string"}, "lte": {"type": "string"}, "gt": {"type": "string"}, "lt": {"type": "string"}, "ne": {"type": "string"}, "contains": {"type": "string"}, "icontains": {"type": "string"}}}}, "top_k": {"title": "Top K", "type": "integer", "default": 10, "description": "The number of top results to return."}, "fields": {"title": "Fields", "type": "array", "items": {"type": "string"}, "description": "A list of field names to include in the response. If not provided, all fields will be returned."}, "rerank": {"title": "<PERSON><PERSON>", "type": "boolean", "default": false, "description": "Whether to rerank the memories."}, "keyword_search": {"title": "Keyword search", "type": "boolean", "default": false, "description": "Whether to search for memories based on keywords."}, "filter_memories": {"title": "Filter memories", "type": "boolean", "default": false, "description": "Whether to filter the memories."}, "threshold": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "default": 0.3, "description": "The minimum similarity threshold for returned results."}, "org_id": {"title": "Organization id", "type": "string", "nullable": true, "description": "The unique identifier of the organization associated with the memory."}, "project_id": {"title": "Project id", "type": "string", "nullable": true, "description": "The unique identifier of the project associated with the memory."}}}, "CreateRun": {"required": ["run_id"], "type": "object", "properties": {"run_id": {"title": "Run id", "minLength": 1, "type": "string"}, "name": {"title": "Name", "minLength": 1, "type": "string"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {}}}}, "CreateUser": {"required": ["user_id"], "type": "object", "properties": {"user_id": {"title": "User id", "minLength": 1, "type": "string"}, "metadata": {"title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {}}}}, "DeleteMemoriesInput": {"type": "object", "description": "Input for deleting memories associated with a specific user, agent, app, or run.", "properties": {"user_id": {"type": "string", "description": "The unique identifier of the user whose memories should be deleted.", "nullable": true}, "agent_id": {"type": "string", "description": "The unique identifier of the agent whose memories should be deleted.", "nullable": true}, "app_id": {"type": "string", "description": "The unique identifier of the application whose memories should be deleted.", "nullable": true}, "run_id": {"type": "string", "description": "The unique identifier of the run whose memories should be deleted.", "nullable": true}}, "anyOf": [{"required": ["user_id"]}, {"required": ["agent_id"]}, {"required": ["app_id"]}, {"required": ["run_id"]}], "minProperties": 1, "maxProperties": 4}, "GetMemoryInput": {"type": "object", "required": ["memory_id"], "properties": {"memory_id": {"type": "string", "format": "uuid", "description": "The unique identifier of the memory"}}}, "UpdateMemoryInput": {"type": "object", "description": "Input for updating an existing memory.", "required": ["memory_id", "text"], "properties": {"memory_id": {"type": "string", "format": "uuid", "description": "The unique identifier of the memory to update"}, "text": {"type": "string", "description": "The new text content to update the memory with"}}}, "EntityInput": {"type": "object", "description": "Input for specifying an entity.", "required": ["entity_type", "entity_id"], "properties": {"entity_type": {"type": "string", "enum": ["user", "agent", "run", "app"], "description": "The type of the entity"}, "entity_id": {"type": "string", "format": "uuid", "description": "The unique identifier of the entity (memory_id)"}}}}, "securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "API key authentication. Prefix your Mem0 API key with 'Token '. Example: 'Token your_api_key'"}}}, "x-original-swagger-version": "2.0"}