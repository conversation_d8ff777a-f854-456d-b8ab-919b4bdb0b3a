---
title: '📝 Text'
---

### Text

Text is a local data type. To supply your own text, use the data_type as `text` and enter a string. The text is not processed, this can be very versatile. Eg:

```python
from embedchain import App

app = App()

app.add('Seek wealth, not money or status. Wealth is having assets that earn while you sleep. Money is how we transfer time and wealth. Status is your place in the social hierarchy.', data_type='text')
```

Note: This is not used in the examples because in most cases you will supply a whole paragraph or file, which did not fit.
