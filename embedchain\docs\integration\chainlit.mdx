---
title: '⛓️ Chainlit'
description: 'Integrate with Chain<PERSON> to create LLM chat apps'
---

In this example, we will learn how to use Chainlit and Embedchain together.

![chainlit-demo](https://github.com/embedchain/embedchain/assets/73601258/d6635624-5cdb-485b-bfbd-3b7c8f18bfff)

## Setup

First, install the required packages:

```bash
pip install embedchain chainlit
```

## Create a Chainlit app

Create a new file called `app.py` and add the following code:

```python
import chainlit as cl
from embedchain import App

import os

os.environ["OPENAI_API_KEY"] = "sk-xxx"

@cl.on_chat_start
async def on_chat_start():
    app = App.from_config(config={
        'app': {
            'config': {
                'name': 'chainlit-app'
            }
        },
        'llm': {
            'config': {
                'stream': True,
            }
        }
    })
    # import your data here
    app.add("https://www.forbes.com/profile/elon-musk/")
    app.collect_metrics = False
    cl.user_session.set("app", app)


@cl.on_message
async def on_message(message: cl.Message):
    app = cl.user_session.get("app")
    msg = cl.Message(content="")
    for chunk in await cl.make_async(app.chat)(message.content):
        await msg.stream_token(chunk)
    
    await msg.send()
```

## Run the app

```
chainlit run app.py
```

## Try it out

Open the app in your browser and start chatting with it!
